import FightEvent from "../types/FightEvent";
import { IBullet, IBulletConfig, IBulletTrajectory, BulletType, TrajectoryType, IBulletLauncher } from "../types/IBullet";
import { ICharacter } from "../types/ICharacter";
import { EventManager } from "./EventManager";


/**
 * 子弹系统实现
 * 提供基础的子弹发射和管理功能
 */
export class Bullet implements IBullet {
    private _id: string;
    private _config: IBulletConfig;
    private _caster: ICharacter;
    private _target?: ICharacter;
    private _targetPosition?: cc.Vec3;
    private _node: cc.Node;
    private _firePosition: cc.Vec3;
    private _timeElapsed: number = 0;
    private _remainingHits: number;
    private _hasCollided: boolean = false;
    private _isDestroyed: boolean = false;
    private _trajectory: IBulletTrajectory;
    private _eventManager: EventManager;

    constructor(id: string, config: IBulletConfig, caster: <PERSON>Character, firePosition: cc.Vec3, target?: ICharacter, targetPosition?: cc.Vec3) {
        this._id = id;
        this._config = config;
        this._caster = caster;
        this._target = target;
        this._targetPosition = targetPosition;
        this._firePosition = firePosition;
        this._remainingHits = config.maxHits;
        this._eventManager = new EventManager();
        // 创建轨迹实例
        this._trajectory = this.createTrajectory();
        this.createBulletNode();
    }

    // 实现IBullet接口
    get id(): string { return this._id; }
    get type(): BulletType { return this._config.type; }
    get node(): cc.Node { return this._node; }
    get caster(): ICharacter { return this._caster; }
    get target(): ICharacter | undefined { return this._target; }
    get targetPosition(): cc.Vec3 | undefined { return this._targetPosition; }
    get firePosition(): cc.Vec3 { return this._firePosition; }
    get currentPosition(): cc.Vec3 { return this._node ? this._node.position : cc.Vec3.ZERO; }
    get speed(): number { return this._config.speed; }
    get lifeTime(): number { return this._config.lifeTime; }
    get timeElapsed(): number { return this._timeElapsed; }
    set timeElapsed(value: number) { this._timeElapsed = value; }
    get remainingHits(): number { return this._remainingHits; }
    set remainingHits(value: number) { this._remainingHits = value; }
    get maxHits(): number { return this._config.maxHits; }
    get hasCollided(): boolean { return this._hasCollided; }
    set hasCollided(value: boolean) { this._hasCollided = value; }
    get isDestroyed(): boolean { return this._isDestroyed; }
    get trajectory(): IBulletTrajectory { return this._trajectory; }

    /*** 更新子弹*/
    update(deltaTime: number): boolean {
        if (this._isDestroyed) {
            return true; // 已销毁
        }
        this._timeElapsed += deltaTime;
        // 检查生命周期
        if (this._timeElapsed >= this._config.lifeTime) {
            this.destroy();
            return true;
        }
        // 更新位置
        this.updateMovement(deltaTime);
        // 检查碰撞
        this.checkCollision();
        return false;
    }

    /** * 命中目标 */
    hit(target: ICharacter): boolean {
        if (this._isDestroyed || this._remainingHits <= 0) {
            return false;
        }
        // 造成伤害
        const damage = this.calculateDamage();
        target.takeDamage(damage, this._caster);
        this._remainingHits--;
        this._hasCollided = true;
        console.log(`Bullet ${this._id} hit ${target.name} for ${damage} damage`);
        // 播放命中音效
        if (this._config.audio?.hitSound) {
            console.log(`Playing hit sound: ${this._config.audio.hitSound}`);
        }
        // 触发命中事件
        this._eventManager.emit(FightEvent.bulletHit, { bullet: this, target: target, damage: damage });
        // 检查是否需要销毁
        if (!this._config.collision?.piercing || this._remainingHits <= 0) {
            this.destroy();
        }
        return true;
    }
    /** * 设置目标 */
    setTarget(target: ICharacter): void {
        this._target = target;
    }
    /** * 设置目标位置 */
    setTargetPosition(position: cc.Vec3): void {
        this._targetPosition = position;
    }
    /**  * 销毁子弹  */
    destroy(): void {
        if (this._isDestroyed) return;
        this._isDestroyed = true;
        if (this._node && this._node.isValid) {
            this._node.destroy();
        }
        this._eventManager.emit(FightEvent.bulletDestroyed, { bullet: this });
        this._eventManager.cleanup();
    }

    /** * 创建轨迹实例 */
    private createTrajectory(): IBulletTrajectory {
        // 简化实现，返回一个基础轨迹 
        return {
            type: this._config.trajectory.type,
            calculateNextPosition: (_bullet: IBullet, deltaTime: number) => {
                return this.calculateNextPosition(deltaTime);
            },
            calculateRotation: (_bullet: IBullet) => {
                return 0; // 简化实现
            },
            hasReachedTarget: (bullet: IBullet, threshold: number) => {
                if (!this._target) return false;
                const distance = cc.Vec3.distance(
                    bullet.currentPosition,
                    this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO)
                );
                return distance <= threshold;
            }
        };
    }
    /** * 计算下一帧位置 */
    private calculateNextPosition(deltaTime: number): cc.Vec3 {
        if (!this._node) return cc.Vec3.ZERO;
        const currentPos = this._node.position;
        let targetPos: cc.Vec3;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        } else if (this._targetPosition) {
            targetPos = this._targetPosition;
        } else {
            return currentPos; // 没有目标，保持当前位置
        }
        // 计算移动方向和距离
        const direction = targetPos.subtract(currentPos).normalize();
        const moveDistance = this._config.speed * deltaTime;
        return currentPos.add(direction.multiplyScalar(moveDistance));
    }
    /** * 创建子弹节点 */
    private createBulletNode(): void {
        this._node = new cc.Node(`Bullet_${this._id}`);
        // 添加精灵组件
        this._node.addComponent(cc.Sprite);
        // 这里应该加载子弹贴图
        // cc.resources.load(this._config.prefabPath, cc.SpriteFrame, (err, spriteFrame) => {
        //     if (!err && sprite.isValid) {
        //         sprite.spriteFrame = spriteFrame;
        //     }
        // });
        // 设置初始位置
        this._node.position = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 添加到场景
        cc.director.getScene().addChild(this._node);
    }
    /** * 更新移动 */
    private updateMovement(deltaTime: number): void {
        if (!this._node || !this._node.isValid) return;
        const currentPos = this._node.position;
        let targetPos: cc.Vec3;
        // 确定目标位置
        if (this._target && !this._target.isDead) {
            targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        } else if (this._targetPosition) {
            targetPos = this._targetPosition;
        } else {
            // 没有目标，直接销毁
            this.destroy();
            return;
        }
        // 计算移动方向
        const direction = targetPos.subtract(currentPos).normalize();
        const moveDistance = this._config.speed * deltaTime;
        // 根据轨迹类型移动
        switch (this._config.trajectory.type) {
            case TrajectoryType.LINEAR:
                this.moveLinear(direction, moveDistance);
                break;
            case TrajectoryType.PARABOLIC:
                this.moveParabolic(targetPos, deltaTime);
                break;
            case TrajectoryType.HOMING:
                this.moveHoming(targetPos, deltaTime);
                break;
            default: this.moveLinear(direction, moveDistance); break;
        }
        // 检查是否到达目标
        const distanceToTarget = cc.Vec3.distance(this._node.position, targetPos);
        if (distanceToTarget <= 10) { // 10像素的容错范围
            this.onHitTarget();
        }
    }
    /*** 线性移动*/
    private moveLinear(direction: cc.Vec3, distance: number): void {
        const newPos = this._node.position.add(direction.multiplyScalar(distance));
        this._node.position = newPos;
    }
    /*** 抛物线移动(简化的抛物线实现)*/
    private moveParabolic(targetPos: cc.Vec3, _deltaTime: number): void {
        const progress = this._timeElapsed / this._config.lifeTime;
        const startPos = this._caster.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
        // 计算抛物线轨迹
        const midPoint = startPos.add(targetPos).multiplyScalar(0.5);
        midPoint.y += 100; // 抛物线高度
        const x = cc.misc.lerp(startPos.x, targetPos.x, progress);
        const y = this.calculateParabolicY(startPos, midPoint, targetPos, progress);
        this._node.position = cc.v3(x, y, 0);
    }
    /** * 追踪移动 */
    private moveHoming(targetPos: cc.Vec3, deltaTime: number): void {
        const currentPos = this._node.position;
        const direction = targetPos.subtract(currentPos).normalize();
        // 追踪子弹有更强的转向能力
        const moveDistance = this._config.speed * deltaTime;
        const newPos = currentPos.add(direction.multiplyScalar(moveDistance));
        this._node.position = newPos;
    }
    /** * 计算抛物线Y坐标 */
    private calculateParabolicY(start: cc.Vec3, mid: cc.Vec3, end: cc.Vec3, t: number): number {
        // 二次贝塞尔曲线
        const oneMinusT = 1 - t;
        return oneMinusT * oneMinusT * start.y + 2 * oneMinusT * t * mid.y + t * t * end.y;
    }
    /** * 检查碰撞 */
    private checkCollision(): void {
        if (!this._config.collision) return;
        // 简化的碰撞检测
        const bulletPos = this._node.position;
        const collisionRadius = this._config.collision.radius;
        // 这里应该使用物理系统或碰撞检测系统
        // 现在简化为距离检测
        if (this._target && !this._target.isDead) {
            const targetPos = this._target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
            const distance = cc.Vec3.distance(bulletPos, targetPos);
            if (distance <= collisionRadius) {
                this.onHitTarget();
            }
        }
    }
    /*** 命中目标*/
    private onHitTarget(): void {
        if (!this._target) {
            return;
        }
        // 使用hit方法处理命中逻辑
        this.hit(this._target);
    }
    /** * 计算伤害 */
    private calculateDamage(): number {
        // 简化的伤害计算，使用施法者的攻击力
        const baseDamage = this._caster.attributes.attack;
        // 这里可以添加更复杂的伤害计算逻辑
        return Math.floor(baseDamage);
    }
    /** * 设置事件监听器 */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }
    /*** 移除事件监听器*/
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }
}

/*** 子弹发射器实现类*/
export class BulletLauncher implements IBulletLauncher {
    private _id: string;
    private _caster: ICharacter;
    private _bulletConfig: IBulletConfig;
    private _firePosition: cc.Vec3 = cc.Vec3.ZERO;
    private _fireDirection: cc.Vec3 = cc.Vec3.ZERO;
    private _fireAngle: number = 0;
    private _fireSpeed: number = 400;
    private _eventManager: EventManager;
    constructor(id: string, caster: ICharacter, bulletConfig: IBulletConfig) {
        this._id = id;
        this._caster = caster;
        this._bulletConfig = bulletConfig;
        this._eventManager = new EventManager();
    }

    // 实现IBulletLauncher接口
    get id(): string { return this._id; }
    get caster(): ICharacter { return this._caster; }
    get bulletConfig(): IBulletConfig { return this._bulletConfig; }
    get firePosition(): cc.Vec3 { return this._firePosition; }
    set firePosition(value: cc.Vec3) { this._firePosition = value; }
    get fireDirection(): cc.Vec3 { return this._fireDirection; }
    set fireDirection(value: cc.Vec3) { this._fireDirection = value; }
    get fireAngle(): number { return this._fireAngle; }
    set fireAngle(value: number) { this._fireAngle = value; }
    get fireSpeed(): number { return this._fireSpeed; }
    set fireSpeed(value: number) { this._fireSpeed = value; }
    get bulletTimesp() { return `${Date.now()}_${Math.random().toString(36).substring(2, 11)}` }
    /*** 发射单个子弹*/
    fire(target?: ICharacter, targetPosition?: cc.Vec3): IBullet | null {
        if (!target && !targetPosition) {
            console.warn("BulletLauncher: No target or target position provided");
            return null;
        }
        const bulletId = `${this._id}_bullet_${this.bulletTimesp}`;
        const bullet = new Bullet(bulletId, this._bulletConfig, this._caster, this._firePosition, target, targetPosition);
        console.log(`BulletLauncher ${this._id} fired bullet ${bulletId}`);
        this._eventManager.emit(FightEvent.bulletFired, { launcher: this, bullet: bullet, target: target, targetPosition: targetPosition });
        return bullet;
    }
    /*** 发射多个子弹（散射）*/
    fireBurst(count: number, spread: number, target?: ICharacter, targetPosition?: cc.Vec3): IBullet[] {
        const bullets: IBullet[] = [];
        if (count <= 0) return bullets;
        // 计算散射角度
        const startAngle = -spread / 2;
        const angleStep = count > 1 ? spread / (count - 1) : 0;
        for (let i = 0; i < count; i++) {
            const angle = startAngle + angleStep * i;
            // 计算散射后的目标位置
            let burstTargetPos: cc.Vec3;
            if (targetPosition) {
                burstTargetPos = this.calculateBurstPosition(targetPosition, angle);
            } else if (target) {
                const originalPos = target.node.convertToWorldSpaceAR(cc.Vec3.ZERO);
                burstTargetPos = this.calculateBurstPosition(originalPos, angle);
            } else {
                continue;
            }
            const bullet = this.fire(undefined, burstTargetPos);
            if (bullet) {
                bullets.push(bullet);
            }
        }
        console.log(`BulletLauncher ${this._id} fired burst of ${bullets.length} bullets`);
        return bullets;
    }

    /** * 设置发射参数 */
    setFireParams(position: cc.Vec3, direction: cc.Vec3, speed?: number): void {
        this._firePosition = position;
        this._fireDirection = direction.normalize();
        if (speed !== undefined) {
            this._fireSpeed = speed;
        }
        // 计算角度
        this._fireAngle = Math.atan2(direction.y, direction.x) * 180 / Math.PI;
    }
    /** * 计算散射位置 */
    private calculateBurstPosition(originalPos: cc.Vec3, angleOffset: number): cc.Vec3 {
        const distance = cc.Vec3.distance(this._firePosition, originalPos);
        const originalAngle = Math.atan2(originalPos.y - this._firePosition.y, originalPos.x - this._firePosition.x);
        const newAngle = originalAngle + angleOffset * Math.PI / 180;
        return cc.v3(this._firePosition.x + Math.cos(newAngle) * distance, this._firePosition.y + Math.sin(newAngle) * distance, originalPos.z);
    }
    /** * 更新子弹配置 */
    updateBulletConfig(config: Partial<IBulletConfig>): void {
        this._bulletConfig = { ...this._bulletConfig, ...config };
    }
    /** * 设置事件监听器 */
    on(event: string, callback: Function): void {
        this._eventManager.on(event, callback);
    }
    /** * 移除事件监听器 */
    off(event: string, callback: Function): void {
        this._eventManager.off(event, callback);
    }
    /**  * 清理资源  */
    cleanup(): void {
        this._eventManager.cleanup();
    }
}
