import { EventManager } from "./EventManager";

/**
 * 统一的事件系统
 * 提供全局事件管理和局部事件管理
 */
export class EventSystem {
    private static _globalEventManager: EventManager;

    /** 获取全局事件管理器 */
    static getGlobal(): EventManager {
        if (!EventSystem._globalEventManager) {
            EventSystem._globalEventManager = new EventManager("global");
        }
        return EventSystem._globalEventManager;
    }

    /** 创建局部事件管理器 */
    static createLocal(instanceId?: string): EventManager {
        return new EventManager(instanceId);
    }

    /** 全局事件快捷方法 */
    static on(event: string, callback: Function, context?: any): void {
        EventSystem.getGlobal().on(event, callback, context);
    }

    static once(event: string, callback: Function, context?: any): void {
        EventSystem.getGlobal().once(event, callback, context);
    }

    static off(event: string, callback?: Function, context?: any): void {
        EventSystem.getGlobal().off(event, callback, context);
    }

    static emit(event: string, data?: any): void {
        EventSystem.getGlobal().emit(event, data);
    }

    static hasListeners(event: string): boolean {
        return EventSystem.getGlobal().hasListeners(event);
    }

    static getListenerCount(event: string): number {
        return EventSystem.getGlobal().getListenerCount(event);
    }

    static getEventNames(): string[] {
        return EventSystem.getGlobal().getEventNames();
    }

    static cleanup(): void {
        if (EventSystem._globalEventManager) {
            EventSystem._globalEventManager.cleanup();
            EventSystem._globalEventManager = null;
        }
    }

    static getDebugInfo(): any {
        return EventSystem.getGlobal().getDebugInfo();
    }
}

/**
 * 事件发射器 Mixin
 * 为类添加局部事件功能
 */
export function withEvents<T extends new (...args: any[]) => {}>(Base: T, instanceId?: string) {
    return class extends Base {
        private _eventManager: EventManager;

        constructor(...args: any[]) {
            super(...args);
            this._eventManager = EventSystem.createLocal(instanceId);
        }

        on(event: string, callback: Function, context?: any): void {
            this._eventManager.on(event, callback, context);
        }

        once(event: string, callback: Function, context?: any): void {
            this._eventManager.once(event, callback, context);
        }

        off(event: string, callback?: Function, context?: any): void {
            this._eventManager.off(event, callback, context);
        }

        protected emit(event: string, data?: any): void {
            this._eventManager.emit(event, data);
        }

        hasListeners(event: string): boolean {
            return this._eventManager.hasListeners(event);
        }

        getListenerCount(event: string): number {
            return this._eventManager.getListenerCount(event);
        }

        getEventNames(): string[] {
            return this._eventManager.getEventNames();
        }

        removeAllListeners(): void {
            this._eventManager.removeAllListeners();
        }

        removeAllListenersForEvent(event: string): void {
            this._eventManager.removeAllListenersForEvent(event);
        }

        protected cleanupEvents(): void {
            this._eventManager.cleanup();
        }

        getEventDebugInfo(): any {
            return this._eventManager.getDebugInfo();
        }
    };
}

/**
 * 事件发射器基类
 * 为需要事件功能的类提供继承
 */
export abstract class EventEmitter {
    private _eventManager: EventManager;

    constructor(instanceId?: string) {
        this._eventManager = EventSystem.createLocal(instanceId);
    }

    on(event: string, callback: Function, context?: any): void {
        this._eventManager.on(event, callback, context);
    }

    once(event: string, callback: Function, context?: any): void {
        this._eventManager.once(event, callback, context);
    }

    off(event: string, callback?: Function, context?: any): void {
        this._eventManager.off(event, callback, context);
    }

    protected emit(event: string, data?: any): void {
        this._eventManager.emit(event, data);
    }

    hasListeners(event: string): boolean {
        return this._eventManager.hasListeners(event);
    }

    getListenerCount(event: string): number {
        return this._eventManager.getListenerCount(event);
    }

    getEventNames(): string[] {
        return this._eventManager.getEventNames();
    }

    removeAllListeners(): void {
        this._eventManager.removeAllListeners();
    }

    removeAllListenersForEvent(event: string): void {
        this._eventManager.removeAllListenersForEvent(event);
    }

    protected cleanupEvents(): void {
        this._eventManager.cleanup();
    }

    getEventDebugInfo(): any {
        return this._eventManager.getDebugInfo();
    }
}

// 全局事件的便捷导出
export const Events = EventSystem;
