import { EventManager } from "./EventManager";

/**
 * 全局事件总线
 * 提供全局事件的统一管理和访问
 */
export class GlobalEventBus {
    private static _instance: GlobalEventBus;
    private _globalEventManager: EventManager;

    constructor() {
        this._globalEventManager = EventManager.getGlobalInstance();
    }

    /** 获取单例实例 */
    static getInstance(): GlobalEventBus {
        if (!GlobalEventBus._instance) {
            GlobalEventBus._instance = new GlobalEventBus();
        }
        return GlobalEventBus._instance;
    }

    /** 获取全局事件管理器 */
    get eventManager(): EventManager {
        return this._globalEventManager;
    }

    /** 注册全局事件监听器 */
    on(event: string, callback: Function, context?: any): void {
        this._globalEventManager.on(event, callback, context);
    }

    /** 注册一次性全局事件监听器 */
    once(event: string, callback: Function, context?: any): void {
        this._globalEventManager.once(event, callback, context);
    }

    /** 移除全局事件监听器 */
    off(event: string, callback?: Function, context?: any): void {
        this._globalEventManager.off(event, callback, context);
    }

    /** 触发全局事件 */
    emit(event: string, data?: any): void {
        this._globalEventManager.emit(event, data);
    }

    /** 检查是否有指定事件的监听器 */
    hasListeners(event: string): boolean {
        return this._globalEventManager.hasListeners(event);
    }

    /** 获取指定事件的监听器数量 */
    getListenerCount(event: string): number {
        return this._globalEventManager.getListenerCount(event);
    }

    /** 获取所有事件名称 */
    getEventNames(): string[] {
        return this._globalEventManager.getEventNames();
    }

    /** 获取调试信息 */
    getDebugInfo(): any {
        return {
            instanceId: this._globalEventManager.instanceId,
            events: this._globalEventManager.getDebugInfo()
        };
    }

    /** 打印调试信息 */
    printDebugInfo(): void {
        console.log("GlobalEventBus Debug Info:", this.getDebugInfo());
    }
}

/**
 * 全局事件总线的便捷访问器
 */
export const globalEventBus = GlobalEventBus.getInstance();
