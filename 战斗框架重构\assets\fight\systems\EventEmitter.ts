import { EventManager } from "./EventManager";

/**
 * 事件发射器基类
 * 为需要事件功能的类提供统一的事件接口
 */
export abstract class EventEmitter {
    protected _eventManager: EventManager;

    constructor(instanceId?: string) {
        this._eventManager = new EventManager(instanceId);
    }

    /**
     * 注册事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    on(event: string, callback: Function, context?: any): void {
        this._eventManager.on(event, callback, context);
    }

    /**
     * 注册一次性事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    once(event: string, callback: Function, context?: any): void {
        this._eventManager.once(event, callback, context);
    }

    /**
     * 移除事件监听器
     * @param event 事件名称
     * @param callback 回调函数
     * @param context 上下文对象
     */
    off(event: string, callback?: Function, context?: any): void {
        this._eventManager.off(event, callback, context);
    }

    /**
     * 触发事件
     * @param event 事件名称
     * @param data 事件数据
     */
    protected emit(event: string, data?: any): void {
        this._eventManager.emit(event, data);
    }

    /**
     * 检查是否有指定事件的监听器
     * @param event 事件名称
     * @returns 是否有监听器
     */
    hasListeners(event: string): boolean {
        return this._eventManager.hasListeners(event);
    }

    /**
     * 获取指定事件的监听器数量
     * @param event 事件名称
     * @returns 监听器数量
     */
    getListenerCount(event: string): number {
        return this._eventManager.getListenerCount(event);
    }

    /**
     * 获取所有事件名称
     * @returns 事件名称数组
     */
    getEventNames(): string[] {
        return this._eventManager.getEventNames();
    }

    /**
     * 移除所有监听器
     */
    removeAllListeners(): void {
        this._eventManager.removeAllListeners();
    }

    /**
     * 移除指定事件的所有监听器
     * @param event 事件名称
     */
    removeAllListenersForEvent(event: string): void {
        this._eventManager.removeAllListenersForEvent(event);
    }

    /**
     * 清理事件管理器
     */
    protected cleanupEvents(): void {
        this._eventManager.cleanup();
    }

    /**
     * 获取事件管理器的调试信息
     */
    getEventDebugInfo(): any {
        return this._eventManager.getDebugInfo();
    }
}

/**
 * 事件发射器接口
 * 为不能继承 EventEmitter 的类提供接口约束
 */
export interface IEventEmitter {
    on(event: string, callback: Function, context?: any): void;
    once(event: string, callback: Function, context?: any): void;
    off(event: string, callback?: Function, context?: any): void;
    hasListeners(event: string): boolean;
    getListenerCount(event: string): number;
    getEventNames(): string[];
    removeAllListeners(): void;
    removeAllListenersForEvent(event: string): void;
}

/**
 * 事件发射器 Mixin
 * 为已有类添加事件功能的混入函数
 */
export function withEventEmitter<T extends new (...args: any[]) => {}>(Base: T, instanceId?: string) {
    return class extends Base implements IEventEmitter {
        private _eventManager: EventManager;

        constructor(...args: any[]) {
            super(...args);
            this._eventManager = new EventManager(instanceId);
        }

        on(event: string, callback: Function, context?: any): void {
            this._eventManager.on(event, callback, context);
        }

        once(event: string, callback: Function, context?: any): void {
            this._eventManager.once(event, callback, context);
        }

        off(event: string, callback?: Function, context?: any): void {
            this._eventManager.off(event, callback, context);
        }

        protected emit(event: string, data?: any): void {
            this._eventManager.emit(event, data);
        }

        hasListeners(event: string): boolean {
            return this._eventManager.hasListeners(event);
        }

        getListenerCount(event: string): number {
            return this._eventManager.getListenerCount(event);
        }

        getEventNames(): string[] {
            return this._eventManager.getEventNames();
        }

        removeAllListeners(): void {
            this._eventManager.removeAllListeners();
        }

        removeAllListenersForEvent(event: string): void {
            this._eventManager.removeAllListenersForEvent(event);
        }

        protected cleanupEvents(): void {
            this._eventManager.cleanup();
        }

        getEventDebugInfo(): any {
            return this._eventManager.getDebugInfo();
        }
    };
}
